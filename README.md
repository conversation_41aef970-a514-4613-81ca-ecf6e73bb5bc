# 🤖 AI AutoTrader Pro

**Comprehensive Automated Trading System with AI Signal Verification**

## 🚀 Quick Start

### Option 1: GUI Interface (Recommended)
```bash
# Double-click this file or run:
python launch_gui.py

# Or use the batch file on Windows:
launch_gui.bat
```

### Option 2: Command Line Interface
```bash
# Live trading mode
python main.py --mode live

# Backtesting mode (coming in Phase 2)
python main.py --mode backtest

# Test API connections
python main.py --test
```

## 📋 Features

### ✅ Currently Available (Phase 1 Complete)
- **Real API Integration**: Connected to Alpaca (paper trading) and Financial Modeling Prep
- **Desktop GUI Interface**: Professional trading dashboard with real-time updates
- **Live Market Data**: Real-time and historical data from multiple sources
- **Order Management**: Place, cancel, and monitor trading orders
- **Account Monitoring**: Real-time account status and position tracking
- **Risk Management**: Market hours detection and account validation
- **Professional Logging**: Comprehensive logging system for all activities
- **Multiple Data Sources**: Primary + fallback data sources for reliability

### 🚧 Coming Soon
- **Phase 2**: Historical Backtesting Engine
- **Phase 3**: AI Signal Verification System
- **Phase 4**: Advanced Analytics & Reporting

## 🖥️ GUI Interface Features

The desktop GUI provides:

### 📊 Account Dashboard
- Real-time account equity, cash, and buying power
- Market status indicator (Open/Closed)
- Current positions count and daily P&L
- Live position tracking with unrealized gains/losses

### 🎛️ Trading Controls
- **Start/Stop Trading**: Begin or halt automated trading
- **Refresh Data**: Update all account and market information
- **Test APIs**: Verify all API connections are working

### 📈 Position Management
- Live positions table showing:
  - Symbol and quantity
  - Average price and current price
  - Unrealized profit/loss for each position

### 📝 Activity Log
- Real-time activity feed showing:
  - Trading signals and executions
  - API status updates
  - Error messages and warnings
  - System status changes

## 🔧 Configuration

### API Keys (Already Configured)
- **Alpaca Trading**: Paper trading account with $30,000 starting capital
- **Financial Modeling Prep**: Fundamental data and company information

### Trading Settings
Edit `config.py` to customize:
```python
TRADING_CONFIG = {
    'scan_interval': 60,        # Seconds between scans
    'trade_quantity': 10,       # Shares per trade
    'take_profit': 1.00,        # Take profit in dollars
    'stop_loss': 0.50,          # Stop loss in dollars
    'max_positions': 5,         # Maximum concurrent positions
    'risk_per_trade': 0.02,     # 2% risk per trade
}
```

## 📊 Current Account Status

- **Account Type**: Paper Trading (Safe for testing)
- **Starting Capital**: $30,000
- **Buying Power**: $60,000
- **API Status**: ✅ Connected and Verified

## 🧪 Testing

### Test API Connections
```bash
python test_api_connections.py
```

### Test GUI Components
```bash
python test_gui.py
```

### Run Full System Test
```bash
python main.py --test
```

## 📁 Project Structure

```
ai_autotrader/
├── 🖥️ GUI Interface
│   ├── gui/main_window.py      # Main GUI application
│   └── launch_gui.py           # GUI launcher
├── 🔌 API Integration
│   ├── api/alpaca_client.py    # Alpaca trading API
│   └── api/fmp_client.py       # Financial data API
├── 📊 Core Trading System
│   ├── main.py                 # Main application
│   ├── screener.py             # Signal detection
│   ├── trade_executor.py       # Order execution
│   ├── risk_manager.py         # Risk management
│   ├── pnl_tracker.py          # P&L tracking
│   └── data_feed.py            # Market data
├── ⚙️ Configuration & Utils
│   ├── config.py               # System configuration
│   └── utils/logger.py         # Logging system
├── 🧪 Testing
│   ├── test_api_connections.py # API tests
│   └── test_gui.py             # GUI tests
└── 📋 Documentation
    └── README.md               # This file
```

## 🚨 Important Notes

### Safety Features
- **Paper Trading Only**: All trades are simulated - no real money at risk
- **Market Hours Detection**: System won't trade when markets are closed
- **Account Validation**: Checks buying power before placing orders
- **Comprehensive Logging**: All activities are logged for review

### Market Hours
- The system automatically detects market hours
- When markets are closed, you'll see a warning message
- Use backtesting mode to test strategies with historical data

## 🔍 Monitoring & Logs

All system activities are logged in the `logs/` directory:
- `trading_system.log` - General system logs
- `trading.log` - Trading-specific activities
- `errors.log` - Error messages only
- `performance.log` - Performance metrics

## 🆘 Troubleshooting

### GUI Won't Start
1. Make sure Python and tkinter are installed
2. Run `python test_gui.py` to test GUI components
3. Check the logs for error messages

### API Connection Issues
1. Run `python test_api_connections.py`
2. Check your internet connection
3. Verify API keys in `config.py`

### Trading Not Working
1. Check if market is open (GUI shows market status)
2. Verify account has sufficient buying power
3. Check the activity log for error messages

## 📞 Support

- Check the `logs/` directory for detailed error information
- All API connections are tested and verified working
- The system uses paper trading - no real money is at risk

---

**🎉 Congratulations! Your AI AutoTrader Pro system is ready to use!**

Start with the GUI interface by running `python launch_gui.py` or double-clicking `launch_gui.bat`
