"""
Alpaca API Client for trading operations and data retrieval
"""
import alpaca_trade_api as tradeapi
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional
from config import ALPACA_CONFIG

logger = logging.getLogger(__name__)

class AlpacaClient:
    """
    Alpaca API client for trading operations and market data
    """
    
    def __init__(self):
        """Initialize Alpaca API client"""
        try:
            self.api = tradeapi.REST(
                ALPACA_CONFIG['api_key'],
                ALPACA_CONFIG['secret_key'],
                ALPACA_CONFIG['base_url'],
                api_version='v2'
            )
            
            # Test connection
            account = self.api.get_account()
            logger.info(f"Connected to Alpaca API. Account status: {account.status}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Alpaca client: {e}")
            raise
    
    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            account = self.api.get_account()
            return {
                'equity': float(account.equity),
                'cash': float(account.cash),
                'buying_power': float(account.buying_power),
                'day_trade_count': int(getattr(account, 'day_trade_count', 0)),
                'pattern_day_trader': getattr(account, 'pattern_day_trader', False),
                'trading_blocked': getattr(account, 'trading_blocked', False),
                'account_blocked': getattr(account, 'account_blocked', False),
                'status': getattr(account, 'status', 'UNKNOWN')
            }
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}
    
    def get_positions(self) -> List[Dict]:
        """Get current positions"""
        try:
            positions = self.api.list_positions()
            return [
                {
                    'symbol': pos.symbol,
                    'qty': float(pos.qty),
                    'side': pos.side,
                    'market_value': float(pos.market_value),
                    'cost_basis': float(pos.cost_basis),
                    'unrealized_pl': float(pos.unrealized_pl),
                    'unrealized_plpc': float(pos.unrealized_plpc),
                    'current_price': float(pos.current_price)
                }
                for pos in positions
            ]
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    def get_orders(self, status: str = 'all') -> List[Dict]:
        """Get orders with specified status"""
        try:
            orders = self.api.list_orders(status=status)
            return [
                {
                    'id': order.id,
                    'symbol': order.symbol,
                    'qty': float(order.qty),
                    'side': order.side,
                    'order_type': order.order_type,
                    'time_in_force': order.time_in_force,
                    'status': order.status,
                    'filled_qty': float(order.filled_qty or 0),
                    'filled_avg_price': float(order.filled_avg_price or 0),
                    'created_at': order.created_at,
                    'updated_at': order.updated_at
                }
                for order in orders
            ]
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
    
    def place_order(self, symbol: str, qty: int, side: str, 
                   order_type: str = 'market', time_in_force: str = 'day',
                   limit_price: Optional[float] = None,
                   stop_price: Optional[float] = None) -> Optional[Dict]:
        """Place a trading order"""
        try:
            order_params = {
                'symbol': symbol,
                'qty': qty,
                'side': side,
                'type': order_type,
                'time_in_force': time_in_force
            }
            
            if limit_price:
                order_params['limit_price'] = limit_price
            if stop_price:
                order_params['stop_price'] = stop_price
            
            order = self.api.submit_order(**order_params)
            
            logger.info(f"Order placed: {side} {qty} {symbol} at {order_type}")
            
            return {
                'id': order.id,
                'symbol': order.symbol,
                'qty': float(order.qty),
                'side': order.side,
                'order_type': order.order_type,
                'status': order.status,
                'created_at': order.created_at
            }
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            self.api.cancel_order(order_id)
            logger.info(f"Order {order_id} cancelled")
            return True
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    def get_historical_data(self, symbol: str, timeframe: str = '1Day',
                           start: Optional[str] = None, 
                           end: Optional[str] = None,
                           limit: int = 1000) -> pd.DataFrame:
        """Get historical market data"""
        try:
            # Convert timeframe to Alpaca format
            timeframe_map = {
                '1m': '1Min', '5m': '5Min', '15m': '15Min',
                '1h': '1Hour', '1d': '1Day'
            }
            alpaca_timeframe = timeframe_map.get(timeframe, timeframe)
            
            if start and end:
                bars = self.api.get_bars(
                    symbol, alpaca_timeframe, 
                    start=start, end=end, 
                    limit=limit
                ).df
            else:
                bars = self.api.get_bars(
                    symbol, alpaca_timeframe, 
                    limit=limit
                ).df
            
            if bars.empty:
                logger.warning(f"No data returned for {symbol}")
                return pd.DataFrame()
            
            # Rename columns to standard format
            bars.columns = ['open', 'high', 'low', 'close', 'volume', 'trade_count', 'vwap']
            
            return bars[['open', 'high', 'low', 'close', 'volume']]
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_latest_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote for a symbol"""
        try:
            quote = self.api.get_latest_quote(symbol)
            return {
                'symbol': symbol,
                'bid': float(quote.bid_price),
                'ask': float(quote.ask_price),
                'bid_size': int(quote.bid_size),
                'ask_size': int(quote.ask_size),
                'timestamp': quote.timestamp
            }
        except Exception as e:
            logger.error(f"Error getting quote for {symbol}: {e}")
            return None
    
    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            clock = self.api.get_clock()
            return clock.is_open
        except Exception as e:
            logger.error(f"Error checking market status: {e}")
            return False
    
    def get_market_calendar(self, start: str, end: str) -> List[Dict]:
        """Get market calendar"""
        try:
            calendar = self.api.get_calendar(start=start, end=end)
            return [
                {
                    'date': str(day.date),
                    'open': str(day.open),
                    'close': str(day.close)
                }
                for day in calendar
            ]
        except Exception as e:
            logger.error(f"Error getting market calendar: {e}")
            return []
