"""
Test script to verify API connections and basic functionality
"""
import sys
import logging
from utils.logger import setup_logging, get_logger
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient
from data_feed import DataFeed

def test_alpaca_connection():
    """Test Alpaca API connection"""
    logger = get_logger('test_alpaca')
    logger.info("Testing Alpaca API connection...")
    
    try:
        client = AlpacaClient()
        
        # Test account info
        account_info = client.get_account_info()
        if account_info:
            logger.info(f"Account Status: Connected")
            logger.info(f"Equity: ${account_info.get('equity', 0):,.2f}")
            logger.info(f"Cash: ${account_info.get('cash', 0):,.2f}")
            logger.info(f"Buying Power: ${account_info.get('buying_power', 0):,.2f}")
        else:
            logger.error("Failed to get account information")
            return False
        
        # Test market status
        is_open = client.is_market_open()
        logger.info(f"Market Open: {is_open}")
        
        # Test historical data
        df = client.get_historical_data('AAPL', '1Day', limit=5)
        if not df.empty:
            logger.info(f"Historical data retrieved: {len(df)} records")
            logger.info(f"Latest AAPL close: ${df['close'].iloc[-1]:.2f}")
        else:
            logger.warning("No historical data retrieved")
        
        logger.info("Alpaca API test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Alpaca API test failed: {e}")
        return False

def test_fmp_connection():
    """Test Financial Modeling Prep API connection"""
    logger = get_logger('test_fmp')
    logger.info("Testing FMP API connection...")
    
    try:
        client = FMPClient()
        
        # Test company profile
        profile = client.get_company_profile('AAPL')
        if profile:
            logger.info(f"Company: {profile.get('company_name')}")
            logger.info(f"Sector: {profile.get('sector')}")
            logger.info(f"Market Cap: ${profile.get('market_cap', 0):,}")
        else:
            logger.warning("Failed to get company profile")
        
        # Test financial ratios
        ratios = client.get_financial_ratios('AAPL')
        if ratios:
            logger.info(f"P/E Ratio: {ratios.get('pe_ratio', 'N/A')}")
            logger.info(f"ROE: {ratios.get('roe', 'N/A')}")
        else:
            logger.warning("Failed to get financial ratios")
        
        logger.info("FMP API test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"FMP API test failed: {e}")
        return False

def test_data_feed():
    """Test unified data feed"""
    logger = get_logger('test_data_feed')
    logger.info("Testing DataFeed...")
    
    try:
        data_feed = DataFeed()
        
        # Test stock data retrieval
        symbols = ['AAPL', 'MSFT', 'GOOGL']
        
        for symbol in symbols:
            df = data_feed.get_stock_data(symbol, '5m', 10)
            if not df.empty:
                logger.info(f"{symbol}: Retrieved {len(df)} records, Latest close: ${df['close'].iloc[-1]:.2f}")
            else:
                logger.warning(f"{symbol}: No data retrieved")
        
        # Test market status
        is_open = data_feed.is_market_open()
        logger.info(f"Market status: {'Open' if is_open else 'Closed'}")
        
        logger.info("DataFeed test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"DataFeed test failed: {e}")
        return False

def main():
    """Run all API tests"""
    print("=" * 60)
    print("AI AutoTrader - API Connection Tests")
    print("=" * 60)
    
    # Initialize logging
    setup_logging()
    logger = get_logger('main_test')
    
    results = {}
    
    # Test Alpaca API
    print("\n1. Testing Alpaca API...")
    results['alpaca'] = test_alpaca_connection()
    
    # Test FMP API
    print("\n2. Testing Financial Modeling Prep API...")
    results['fmp'] = test_fmp_connection()
    
    # Test DataFeed
    print("\n3. Testing Unified DataFeed...")
    results['data_feed'] = test_data_feed()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "[PASSED]" if result else "[FAILED]"
        print(f"{test_name.upper():<20}: {status}")

    all_passed = all(results.values())

    if all_passed:
        print("\n[SUCCESS] All API tests passed! System is ready for trading.")
        logger.info("All API tests passed successfully")
    else:
        print("\n[WARNING] Some tests failed. Please check the logs for details.")
        logger.warning("Some API tests failed")
    
    print("\nCheck the 'logs' directory for detailed logs.")
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
