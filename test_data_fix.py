"""
Test the fixed data retrieval system
"""
from data_feed import get_stock_data
import pandas as pd

def test_data_retrieval():
    """Test that we can get sufficient data for TTM Squeeze"""
    print("Testing Fixed Data Retrieval System")
    print("=" * 50)
    
    symbols = ['AAPL', 'SPY', 'MSFT', 'QQQ']
    timeframes = ['1d', '1h']
    
    for symbol in symbols:
        print(f"\nTesting {symbol}:")
        
        for timeframe in timeframes:
            try:
                df = get_stock_data(symbol, timeframe=timeframe, period=50)
                
                if not df.empty:
                    print(f"  {timeframe}: {len(df)} bars retrieved")
                    if len(df) >= 25:
                        print(f"    ✅ Sufficient for TTM Squeeze calculation")
                    else:
                        print(f"    ⚠️  Need more data (minimum 25 bars)")
                    
                    # Show date range
                    if len(df) > 0:
                        start_date = df.index[0].strftime('%Y-%m-%d %H:%M')
                        end_date = df.index[-1].strftime('%Y-%m-%d %H:%M')
                        print(f"    📅 Range: {start_date} to {end_date}")
                        print(f"    💰 Latest price: ${df['close'].iloc[-1]:.2f}")
                else:
                    print(f"  {timeframe}: No data available")
                    
            except Exception as e:
                print(f"  {timeframe}: Error - {e}")
    
    print(f"\n" + "=" * 50)
    print("Data retrieval test complete")

if __name__ == "__main__":
    test_data_retrieval()
