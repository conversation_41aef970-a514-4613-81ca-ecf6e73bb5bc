"""
Test that we're using ONLY Alpaca and FMP APIs - NO YFINANCE
"""
from data_feed import DataFeed, get_stock_data
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient
import sys

def test_api_sources():
    """Test that we're only using Alpaca and FMP"""
    print("🔍 TESTING API SOURCES - ALPACA + FMP ONLY")
    print("=" * 60)
    
    # Test DataFeed initialization
    print("1. Testing DataFeed initialization...")
    data_feed = DataFeed()
    
    if hasattr(data_feed, 'yf_fallback'):
        print("❌ ERROR: yfinance fallback still exists!")
        return False
    else:
        print("✅ No yfinance fallback - GOOD!")
    
    # Test API clients
    print("\n2. Testing API clients...")
    
    if data_feed.alpaca_client:
        print("✅ Alpaca client initialized")
    else:
        print("❌ Alpaca client missing")
    
    if data_feed.fmp_client:
        print("✅ FMP client initialized")
    else:
        print("❌ FMP client missing")
    
    # Test data retrieval
    print("\n3. Testing data retrieval...")
    symbols = ['AAPL', 'SPY', 'MSFT']
    
    for symbol in symbols:
        print(f"\n📊 Testing {symbol}:")
        
        # Test different timeframes
        for timeframe in ['1h', '1d']:
            df = data_feed.get_stock_data(symbol, timeframe=timeframe, period=10)
            
            if not df.empty:
                print(f"  ✅ {timeframe}: {len(df)} bars retrieved")
                print(f"     Latest price: ${df['close'].iloc[-1]:.2f}")
            else:
                print(f"  📊 {timeframe}: No data (normal if market closed)")
    
    return True

def test_no_yfinance_imports():
    """Verify no yfinance imports in the codebase"""
    print("\n🔍 CHECKING FOR YFINANCE IMPORTS")
    print("=" * 50)
    
    files_to_check = [
        'data_feed.py',
        'screener.py', 
        'trade_executor.py',
        'main.py'
    ]
    
    yfinance_found = False
    
    for filename in files_to_check:
        try:
            with open(filename, 'r') as f:
                content = f.read()
                
            if 'yfinance' in content or 'import yf' in content:
                print(f"❌ {filename}: Contains yfinance references")
                yfinance_found = True
            else:
                print(f"✅ {filename}: Clean (no yfinance)")
                
        except FileNotFoundError:
            print(f"⚠️  {filename}: File not found")
    
    if not yfinance_found:
        print("\n✅ NO YFINANCE IMPORTS FOUND - CLEAN!")
    else:
        print("\n❌ YFINANCE REFERENCES STILL EXIST")
    
    return not yfinance_found

def test_api_functionality():
    """Test Alpaca and FMP API functionality"""
    print("\n🧪 TESTING API FUNCTIONALITY")
    print("=" * 50)
    
    # Test Alpaca
    print("📊 Testing Alpaca API...")
    try:
        alpaca = AlpacaClient()
        account = alpaca.get_account_info()
        
        if account:
            print(f"✅ Alpaca: Account equity ${account.get('equity', 0):,.2f}")
            
            # Test market data
            df = alpaca.get_historical_data('AAPL', '1Day', limit=5)
            if not df.empty:
                print(f"✅ Alpaca: Retrieved {len(df)} AAPL bars")
            else:
                print("📊 Alpaca: No market data (normal if market closed)")
        else:
            print("❌ Alpaca: Failed to get account info")
            
    except Exception as e:
        print(f"❌ Alpaca: Error - {e}")
    
    # Test FMP
    print("\n💰 Testing FMP API...")
    try:
        fmp = FMPClient()
        profile = fmp.get_company_profile('AAPL')
        
        if profile:
            print(f"✅ FMP: {profile.get('company_name', 'Unknown')} profile retrieved")
            print(f"✅ FMP: Market cap ${profile.get('market_cap', 0):,}")
        else:
            print("❌ FMP: Failed to get company profile")
            
    except Exception as e:
        print(f"❌ FMP: Error - {e}")

def main():
    """Run all tests"""
    print("🎯 AI AUTOTRADER - API SOURCE VERIFICATION")
    print("=" * 80)
    print("Verifying we use ONLY Alpaca and FMP APIs")
    print("NO YFINANCE, NO SIMULATED DATA")
    print("=" * 80)
    
    # Test 1: API sources
    sources_ok = test_api_sources()
    
    # Test 2: No yfinance imports
    imports_clean = test_no_yfinance_imports()
    
    # Test 3: API functionality
    test_api_functionality()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 80)
    
    if sources_ok and imports_clean:
        print("✅ SUCCESS: System uses ONLY Alpaca + FMP APIs")
        print("🚫 NO yfinance dependencies")
        print("🚫 NO simulated data")
        print("✅ 100% real market data from professional APIs")
        print("\n🎯 READY FOR TRADING!")
    else:
        print("❌ ISSUES FOUND:")
        if not sources_ok:
            print("  • Data source configuration problems")
        if not imports_clean:
            print("  • yfinance references still exist")
        print("\n🔧 Please review and fix issues above")
    
    print(f"\n📊 Current data sources:")
    print(f"  • Primary: Alpaca Markets API")
    print(f"  • Secondary: Financial Modeling Prep API") 
    print(f"  • Fallback: None (real data only)")

if __name__ == "__main__":
    main()
