from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Configuration
ALPACA_CONFIG = {
    'api_key': 'PKTDPIE8NCOTF8HYBZAM',
    'secret_key': 'QLhqochMkJz0nDPh3suvk5WWIU0fO5iziDgHqGTB',
    'base_url': 'https://paper-api.alpaca.markets',  # Paper trading URL
    'data_url': 'https://data.alpaca.markets'
}

FMP_CONFIG = {
    'api_key': 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7',
    'base_url': 'https://financialmodelingprep.com/api/v3'
}

# Trading Configuration
TRADING_CONFIG = {
    'scan_interval': 60,  # seconds
    'trade_quantity': 10,
    'take_profit': 1.00,  # $ gain
    'stop_loss': 0.50,    # $ loss
    'max_positions': 5,   # Maximum concurrent positions
    'risk_per_trade': 0.02,  # 2% risk per trade
    'max_daily_loss': 0.05,  # 5% max daily loss
    'trading_hours': {
        'start': '09:30',
        'end': '16:00',
        'timezone': 'US/Eastern'
    }
}

# Backtesting Configuration
BACKTEST_CONFIG = {
    'initial_capital': 100000,  # $100k starting capital
    'commission': 0.0,  # Commission per trade
    'slippage': 0.001,  # 0.1% slippage
    'start_date': '2023-01-01',
    'end_date': '2024-12-31',
    'benchmark': 'SPY'
}

# AI Configuration
AI_CONFIG = {
    'confidence_threshold': 0.7,  # Minimum confidence for trade execution
    'model_retrain_interval': 7,  # Days between model retraining
    'features': [
        'momentum', 'rsi', 'macd', 'bollinger_bands',
        'volume_profile', 'atr', 'support_resistance'
    ]
}

# Database Configuration
DATABASE_CONFIG = {
    'url': 'sqlite:///trading_system.db',
    'echo': False  # Set to True for SQL debugging
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'trading_system.log',
    'max_bytes': 10485760,  # 10MB
    'backup_count': 5
}

# GUI Configuration
GUI_CONFIG = {
    'window_title': 'AI AutoTrader Pro',
    'window_size': '1400x900',
    'theme': 'dark',
    'update_interval': 1000,  # milliseconds
    'chart_timeframes': ['1m', '5m', '15m', '1h', '1d']
}

# Legacy config for backward compatibility
config = TRADING_CONFIG
