"""
Centralized logging configuration for the trading system
"""
import logging
import logging.handlers
import os
from datetime import datetime
from config import LOGGING_CONFIG

def setup_logging():
    """
    Setup centralized logging for the entire trading system
    """
    # Create logs directory if it doesn't exist
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOGGING_CONFIG['level']))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)
    
    # Main log file handler (rotating)
    main_log_file = os.path.join(log_dir, LOGGING_CONFIG['file'])
    file_handler = logging.handlers.RotatingFileHandler(
        main_log_file,
        maxBytes=LOGGING_CONFIG['max_bytes'],
        backupCount=LOGGING_CONFIG['backup_count']
    )
    file_handler.setLevel(getattr(logging, LOGGING_CONFIG['level']))
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Trading-specific log file
    trading_log_file = os.path.join(log_dir, 'trading.log')
    trading_handler = logging.handlers.RotatingFileHandler(
        trading_log_file,
        maxBytes=LOGGING_CONFIG['max_bytes'],
        backupCount=LOGGING_CONFIG['backup_count']
    )
    trading_handler.setLevel(logging.INFO)
    trading_handler.setFormatter(detailed_formatter)
    
    # Add trading handler to specific loggers
    trading_loggers = [
        'trade_executor', 'screener', 'risk_manager', 
        'pnl_tracker', 'api.alpaca_client', 'data_feed'
    ]
    
    for logger_name in trading_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(trading_handler)
    
    # Error log file
    error_log_file = os.path.join(log_dir, 'errors.log')
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=LOGGING_CONFIG['max_bytes'],
        backupCount=LOGGING_CONFIG['backup_count']
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # Performance log file
    performance_log_file = os.path.join(log_dir, 'performance.log')
    performance_handler = logging.handlers.RotatingFileHandler(
        performance_log_file,
        maxBytes=LOGGING_CONFIG['max_bytes'],
        backupCount=LOGGING_CONFIG['backup_count']
    )
    performance_handler.setLevel(logging.INFO)
    performance_handler.setFormatter(detailed_formatter)
    
    # Add performance handler to specific loggers
    performance_logger = logging.getLogger('performance')
    performance_logger.addHandler(performance_handler)
    
    logging.info("Logging system initialized successfully")

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name
    """
    return logging.getLogger(name)

def log_trade_execution(symbol: str, side: str, quantity: int, price: float, order_id: str = None):
    """
    Log trade execution details
    """
    trade_logger = logging.getLogger('trading')
    message = f"TRADE EXECUTED: {side.upper()} {quantity} {symbol} @ ${price:.2f}"
    if order_id:
        message += f" (Order ID: {order_id})"
    trade_logger.info(message)

def log_signal_generated(symbol: str, side: str, confidence: float, indicators: dict):
    """
    Log signal generation details
    """
    signal_logger = logging.getLogger('signals')
    message = f"SIGNAL GENERATED: {side.upper()} {symbol} (Confidence: {confidence:.2f})"
    signal_logger.info(message)
    signal_logger.debug(f"Indicators: {indicators}")

def log_performance_metric(metric_name: str, value: float, timestamp: datetime = None):
    """
    Log performance metrics
    """
    perf_logger = logging.getLogger('performance')
    if timestamp is None:
        timestamp = datetime.now()
    
    message = f"METRIC: {metric_name} = {value:.4f} at {timestamp}"
    perf_logger.info(message)

def log_risk_event(event_type: str, symbol: str, details: dict):
    """
    Log risk management events
    """
    risk_logger = logging.getLogger('risk')
    message = f"RISK EVENT: {event_type} for {symbol}"
    risk_logger.warning(message)
    risk_logger.info(f"Details: {details}")

def log_api_error(api_name: str, endpoint: str, error: str):
    """
    Log API errors
    """
    api_logger = logging.getLogger(f'api.{api_name}')
    message = f"API ERROR: {endpoint} - {error}"
    api_logger.error(message)

# Initialize logging when module is imported
setup_logging()
