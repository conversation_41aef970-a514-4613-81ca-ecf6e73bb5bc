"""
Financial Modeling Prep API Client for fundamental data and additional market information
"""
import requests
import logging
from typing import Dict, List, Optional
from config import FMP_CONFIG

logger = logging.getLogger(__name__)

class FMPClient:
    """
    Financial Modeling Prep API client for fundamental and market data
    """
    
    def __init__(self):
        """Initialize FMP API client"""
        self.api_key = FMP_CONFIG['api_key']
        self.base_url = FMP_CONFIG['base_url']
        self.session = requests.Session()
        
        # Test connection
        try:
            response = self._make_request('/profile/AAPL')
            if response:
                logger.info("Connected to Financial Modeling Prep API")
            else:
                logger.warning("Failed to connect to FMP API")
        except Exception as e:
            logger.error(f"Error initializing FMP client: {e}")
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make API request to FMP"""
        try:
            url = f"{self.base_url}{endpoint}"
            request_params = {'apikey': self.api_key}
            
            if params:
                request_params.update(params)
            
            response = self.session.get(url, params=request_params, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed for {endpoint}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in API request: {e}")
            return None
    
    def get_company_profile(self, symbol: str) -> Optional[Dict]:
        """Get company profile information"""
        try:
            data = self._make_request(f'/profile/{symbol}')
            if data and len(data) > 0:
                profile = data[0]
                return {
                    'symbol': profile.get('symbol'),
                    'company_name': profile.get('companyName'),
                    'sector': profile.get('sector'),
                    'industry': profile.get('industry'),
                    'market_cap': profile.get('mktCap'),
                    'price': profile.get('price'),
                    'beta': profile.get('beta'),
                    'volume_avg': profile.get('volAvg'),
                    'description': profile.get('description')
                }
        except Exception as e:
            logger.error(f"Error getting company profile for {symbol}: {e}")
        return None
    
    def get_financial_ratios(self, symbol: str) -> Optional[Dict]:
        """Get financial ratios for a company"""
        try:
            data = self._make_request(f'/ratios/{symbol}')
            if data and len(data) > 0:
                ratios = data[0]
                return {
                    'symbol': ratios.get('symbol'),
                    'pe_ratio': ratios.get('priceEarningsRatio'),
                    'peg_ratio': ratios.get('pegRatio'),
                    'price_to_book': ratios.get('priceToBookRatio'),
                    'price_to_sales': ratios.get('priceToSalesRatio'),
                    'debt_to_equity': ratios.get('debtEquityRatio'),
                    'current_ratio': ratios.get('currentRatio'),
                    'quick_ratio': ratios.get('quickRatio'),
                    'roe': ratios.get('returnOnEquity'),
                    'roa': ratios.get('returnOnAssets'),
                    'gross_profit_margin': ratios.get('grossProfitMargin'),
                    'operating_profit_margin': ratios.get('operatingProfitMargin'),
                    'net_profit_margin': ratios.get('netProfitMargin')
                }
        except Exception as e:
            logger.error(f"Error getting financial ratios for {symbol}: {e}")
        return None
    
    def get_income_statement(self, symbol: str, period: str = 'annual', limit: int = 5) -> List[Dict]:
        """Get income statement data"""
        try:
            params = {'period': period, 'limit': limit}
            data = self._make_request(f'/income-statement/{symbol}', params)
            
            if data:
                return [
                    {
                        'date': item.get('date'),
                        'revenue': item.get('revenue'),
                        'gross_profit': item.get('grossProfit'),
                        'operating_income': item.get('operatingIncome'),
                        'net_income': item.get('netIncome'),
                        'eps': item.get('eps'),
                        'eps_diluted': item.get('epsdiluted')
                    }
                    for item in data
                ]
        except Exception as e:
            logger.error(f"Error getting income statement for {symbol}: {e}")
        return []
    
    def get_balance_sheet(self, symbol: str, period: str = 'annual', limit: int = 5) -> List[Dict]:
        """Get balance sheet data"""
        try:
            params = {'period': period, 'limit': limit}
            data = self._make_request(f'/balance-sheet-statement/{symbol}', params)
            
            if data:
                return [
                    {
                        'date': item.get('date'),
                        'total_assets': item.get('totalAssets'),
                        'total_liabilities': item.get('totalLiabilities'),
                        'total_equity': item.get('totalStockholdersEquity'),
                        'cash': item.get('cashAndCashEquivalents'),
                        'total_debt': item.get('totalDebt'),
                        'working_capital': item.get('totalAssets', 0) - item.get('totalLiabilities', 0)
                    }
                    for item in data
                ]
        except Exception as e:
            logger.error(f"Error getting balance sheet for {symbol}: {e}")
        return []
    
    def get_cash_flow(self, symbol: str, period: str = 'annual', limit: int = 5) -> List[Dict]:
        """Get cash flow statement data"""
        try:
            params = {'period': period, 'limit': limit}
            data = self._make_request(f'/cash-flow-statement/{symbol}', params)
            
            if data:
                return [
                    {
                        'date': item.get('date'),
                        'operating_cash_flow': item.get('netCashProvidedByOperatingActivities'),
                        'investing_cash_flow': item.get('netCashUsedForInvestingActivites'),
                        'financing_cash_flow': item.get('netCashUsedProvidedByFinancingActivities'),
                        'free_cash_flow': item.get('freeCashFlow'),
                        'capex': item.get('capitalExpenditure')
                    }
                    for item in data
                ]
        except Exception as e:
            logger.error(f"Error getting cash flow for {symbol}: {e}")
        return []
    
    def get_analyst_estimates(self, symbol: str) -> Optional[Dict]:
        """Get analyst estimates"""
        try:
            data = self._make_request(f'/analyst-estimates/{symbol}')
            if data and len(data) > 0:
                estimate = data[0]
                return {
                    'symbol': estimate.get('symbol'),
                    'date': estimate.get('date'),
                    'estimated_revenue_low': estimate.get('estimatedRevenueLow'),
                    'estimated_revenue_high': estimate.get('estimatedRevenueHigh'),
                    'estimated_revenue_avg': estimate.get('estimatedRevenueAvg'),
                    'estimated_eps_avg': estimate.get('estimatedEpsAvg'),
                    'estimated_eps_high': estimate.get('estimatedEpsHigh'),
                    'estimated_eps_low': estimate.get('estimatedEpsLow'),
                    'number_analyst_estimated_revenue': estimate.get('numberAnalystEstimatedRevenue'),
                    'number_analyst_estimated_eps': estimate.get('numberAnalystEstimatedEps')
                }
        except Exception as e:
            logger.error(f"Error getting analyst estimates for {symbol}: {e}")
        return None
    
    def get_stock_screener(self, market_cap_more_than: int = None, 
                          market_cap_lower_than: int = None,
                          price_more_than: float = None,
                          price_lower_than: float = None,
                          beta_more_than: float = None,
                          beta_lower_than: float = None,
                          volume_more_than: int = None,
                          sector: str = None,
                          industry: str = None,
                          dividend_more_than: float = None,
                          limit: int = 100) -> List[Dict]:
        """Screen stocks based on criteria"""
        try:
            params = {'limit': limit}
            
            # Add screening parameters
            if market_cap_more_than:
                params['marketCapMoreThan'] = market_cap_more_than
            if market_cap_lower_than:
                params['marketCapLowerThan'] = market_cap_lower_than
            if price_more_than:
                params['priceMoreThan'] = price_more_than
            if price_lower_than:
                params['priceLowerThan'] = price_lower_than
            if beta_more_than:
                params['betaMoreThan'] = beta_more_than
            if beta_lower_than:
                params['betaLowerThan'] = beta_lower_than
            if volume_more_than:
                params['volumeMoreThan'] = volume_more_than
            if sector:
                params['sector'] = sector
            if industry:
                params['industry'] = industry
            if dividend_more_than:
                params['dividendMoreThan'] = dividend_more_than
            
            data = self._make_request('/stock-screener', params)
            
            if data:
                return [
                    {
                        'symbol': item.get('symbol'),
                        'company_name': item.get('companyName'),
                        'market_cap': item.get('marketCap'),
                        'sector': item.get('sector'),
                        'industry': item.get('industry'),
                        'beta': item.get('beta'),
                        'price': item.get('price'),
                        'volume_avg': item.get('volAvg')
                    }
                    for item in data
                ]
        except Exception as e:
            logger.error(f"Error in stock screener: {e}")
        return []
    
    def get_historical_prices(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get historical daily prices"""
        try:
            params = {'limit': limit}
            data = self._make_request(f'/historical-price-full/{symbol}', params)

            if data and 'historical' in data:
                return data['historical']

        except Exception as e:
            logger.error(f"Error getting historical prices for {symbol}: {e}")
        return []

    def get_market_hours(self) -> Optional[Dict]:
        """Get market hours information"""
        try:
            data = self._make_request('/market-hours')
            if data:
                return data
        except Exception as e:
            logger.error(f"Error getting market hours: {e}")
        return None
