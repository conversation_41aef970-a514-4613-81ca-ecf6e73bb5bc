"""
Main GUI Window for AI AutoTrader
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
from datetime import datetime
from utils.logger import get_logger
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient
from data_feed import DataFeed
from config import TRADING_CONFIG, GUI_CONFIG

class AutoTraderGUI:
    """
    Main GUI application for AI AutoTrader
    """
    
    def __init__(self):
        """Initialize the GUI"""
        self.logger = get_logger('gui')
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_widgets()
        self.setup_api_clients()
        self.is_running = False
        self.trading_thread = None
        
    def setup_window(self):
        """Setup main window properties"""
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.configure(bg='#2b2b2b')  # Dark theme
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        self.root.geometry(f"1400x900+{x}+{y}")
        
    def setup_variables(self):
        """Setup tkinter variables"""
        self.account_equity = tk.StringVar(value="$0.00")
        self.account_cash = tk.StringVar(value="$0.00")
        self.buying_power = tk.StringVar(value="$0.00")
        self.market_status = tk.StringVar(value="Unknown")
        self.total_positions = tk.StringVar(value="0")
        self.daily_pnl = tk.StringVar(value="$0.00")
        self.status_text = tk.StringVar(value="Ready")
        self.scan_progress = tk.DoubleVar(value=0.0)
        self.scan_status = tk.StringVar(value="Idle")
        self.next_scan_countdown = tk.StringVar(value="")
        
    def setup_widgets(self):
        """Setup all GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="🤖 AI AutoTrader Pro", 
                               font=('Arial', 20, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Top panel - Account info and controls
        self.setup_top_panel(main_frame)
        
        # Middle panel - Trading info and logs
        self.setup_middle_panel(main_frame)
        
        # Bottom panel - Status and controls
        self.setup_bottom_panel(main_frame)
        
    def setup_top_panel(self, parent):
        """Setup top panel with account info and controls"""
        top_frame = ttk.LabelFrame(parent, text="Account Information & Controls", padding=10)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Account info (left side)
        account_frame = ttk.Frame(top_frame)
        account_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Account details
        ttk.Label(account_frame, text="Equity:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(account_frame, textvariable=self.account_equity, font=('Arial', 10, 'bold')).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(account_frame, text="Cash:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(account_frame, textvariable=self.account_cash, font=('Arial', 10, 'bold')).grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(account_frame, text="Buying Power:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(account_frame, textvariable=self.buying_power, font=('Arial', 10, 'bold')).grid(row=2, column=1, sticky=tk.W)
        
        ttk.Label(account_frame, text="Market Status:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        ttk.Label(account_frame, textvariable=self.market_status, font=('Arial', 10, 'bold')).grid(row=0, column=3, sticky=tk.W)
        
        ttk.Label(account_frame, text="Positions:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10))
        ttk.Label(account_frame, textvariable=self.total_positions, font=('Arial', 10, 'bold')).grid(row=1, column=3, sticky=tk.W)
        
        ttk.Label(account_frame, text="Daily P&L:").grid(row=2, column=2, sticky=tk.W, padx=(20, 10))
        ttk.Label(account_frame, textvariable=self.daily_pnl, font=('Arial', 10, 'bold')).grid(row=2, column=3, sticky=tk.W)

        # Scanning status section
        ttk.Label(account_frame, text="Scan Status:").grid(row=0, column=4, sticky=tk.W, padx=(20, 10))
        self.scan_status_label = ttk.Label(account_frame, textvariable=self.scan_status, font=('Arial', 10, 'bold'))
        self.scan_status_label.grid(row=0, column=5, sticky=tk.W)

        ttk.Label(account_frame, text="Next Scan:").grid(row=1, column=4, sticky=tk.W, padx=(20, 10))
        ttk.Label(account_frame, textvariable=self.next_scan_countdown, font=('Arial', 10, 'bold')).grid(row=1, column=5, sticky=tk.W)
        
        # Control buttons (right side)
        control_frame = ttk.Frame(top_frame)
        control_frame.pack(side=tk.RIGHT, padx=(20, 0))
        
        self.start_button = ttk.Button(control_frame, text="Start Trading", 
                                      command=self.start_trading, style='Accent.TButton')
        self.start_button.pack(pady=2)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Trading", 
                                     command=self.stop_trading, state=tk.DISABLED)
        self.stop_button.pack(pady=2)
        
        ttk.Button(control_frame, text="Refresh Data", 
                  command=self.refresh_data).pack(pady=2)
        
        ttk.Button(control_frame, text="Test APIs", 
                  command=self.test_apis).pack(pady=2)
        
    def setup_middle_panel(self, parent):
        """Setup middle panel with trading info and logs"""
        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Left side - Positions and Orders
        left_frame = ttk.LabelFrame(middle_frame, text="Positions & Orders", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Positions treeview
        self.positions_tree = ttk.Treeview(left_frame, columns=('Symbol', 'Qty', 'Price', 'P&L'), show='headings', height=8)
        self.positions_tree.heading('Symbol', text='Symbol')
        self.positions_tree.heading('Qty', text='Quantity')
        self.positions_tree.heading('Price', text='Avg Price')
        self.positions_tree.heading('P&L', text='Unrealized P&L')
        
        self.positions_tree.column('Symbol', width=80)
        self.positions_tree.column('Qty', width=80)
        self.positions_tree.column('Price', width=100)
        self.positions_tree.column('P&L', width=120)
        
        self.positions_tree.pack(fill=tk.BOTH, expand=True)
        
        # Right side - Activity Log
        right_frame = ttk.LabelFrame(middle_frame, text="Activity Log", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        self.log_text = scrolledtext.ScrolledText(right_frame, height=15, width=50, 
                                                 bg='#1e1e1e', fg='#ffffff', 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def setup_bottom_panel(self, parent):
        """Setup bottom panel with status and progress"""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X)

        # Progress bar frame
        progress_frame = ttk.LabelFrame(bottom_frame, text="Scanning Progress", padding=5)
        progress_frame.pack(fill=tk.X, pady=(0, 5))

        # Progress bar
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.scan_progress,
                                          maximum=100, length=400, mode='determinate')
        self.progress_bar.pack(side=tk.LEFT, padx=(0, 10))

        # Progress text
        self.progress_text = ttk.Label(progress_frame, text="Ready", font=('Arial', 9))
        self.progress_text.pack(side=tk.LEFT)

        # Status bar
        status_frame = ttk.LabelFrame(bottom_frame, text="Status", padding=5)
        status_frame.pack(fill=tk.X)

        ttk.Label(status_frame, textvariable=self.status_text,
                 font=('Arial', 10)).pack(side=tk.LEFT)

        # Current time
        self.time_label = ttk.Label(status_frame, text="", font=('Arial', 10))
        self.time_label.pack(side=tk.RIGHT)
        
    def setup_api_clients(self):
        """Initialize API clients"""
        try:
            self.alpaca_client = AlpacaClient()
            self.fmp_client = FMPClient()
            self.data_feed = DataFeed()
            self.log_message("✅ API clients initialized successfully")
            self.refresh_data()
        except Exception as e:
            self.log_message(f"❌ Error initializing APIs: {e}")
            messagebox.showerror("API Error", f"Failed to initialize APIs:\n{e}")
    
    def log_message(self, message):
        """Add message to activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep log size manageable
        if self.log_text.index(tk.END).split('.')[0] > '1000':
            self.log_text.delete('1.0', '100.0')
    
    def refresh_data(self):
        """Refresh account and market data"""
        try:
            # Update account info
            account_info = self.alpaca_client.get_account_info()
            if account_info:
                self.account_equity.set(f"${account_info.get('equity', 0):,.2f}")
                self.account_cash.set(f"${account_info.get('cash', 0):,.2f}")
                self.buying_power.set(f"${account_info.get('buying_power', 0):,.2f}")
            
            # Update market status
            is_open = self.data_feed.is_market_open()
            self.market_status.set("🟢 Open" if is_open else "🔴 Closed")
            
            # Update positions
            self.update_positions()
            
            self.log_message("📊 Data refreshed")
            self.status_text.set("Data updated successfully")
            
        except Exception as e:
            self.log_message(f"❌ Error refreshing data: {e}")
            self.status_text.set(f"Error: {e}")
    
    def update_positions(self):
        """Update positions display"""
        try:
            # Clear existing items
            for item in self.positions_tree.get_children():
                self.positions_tree.delete(item)
            
            # Get current positions
            positions = self.alpaca_client.get_positions()
            self.total_positions.set(str(len(positions)))
            
            # Add positions to tree
            total_pnl = 0
            for pos in positions:
                pnl = pos.get('unrealized_pl', 0)
                total_pnl += pnl
                
                self.positions_tree.insert('', tk.END, values=(
                    pos.get('symbol', ''),
                    f"{pos.get('qty', 0):,.0f}",
                    f"${pos.get('current_price', 0):.2f}",
                    f"${pnl:,.2f}"
                ))
            
            self.daily_pnl.set(f"${total_pnl:,.2f}")
            
        except Exception as e:
            self.log_message(f"❌ Error updating positions: {e}")
    
    def test_apis(self):
        """Test API connections"""
        self.log_message("🧪 Testing API connections...")
        self.status_text.set("Testing APIs...")
        self.scan_status.set("🧪 Testing")
        self.progress_text.config(text="Testing API connections...")

        def run_tests():
            try:
                # Test Alpaca
                self.root.after(0, lambda: self.scan_progress.set(25))
                self.root.after(0, lambda: self.progress_text.config(text="Testing Alpaca API..."))

                account = self.alpaca_client.get_account_info()
                if account:
                    self.log_message("✅ Alpaca API: Connected")
                else:
                    self.log_message("❌ Alpaca API: Failed")

                # Test FMP
                self.root.after(0, lambda: self.scan_progress.set(75))
                self.root.after(0, lambda: self.progress_text.config(text="Testing FMP API..."))

                profile = self.fmp_client.get_company_profile('AAPL')
                if profile:
                    self.log_message("✅ FMP API: Connected")
                else:
                    self.log_message("❌ FMP API: Failed")

                # Complete
                self.root.after(0, lambda: self.scan_progress.set(100))
                self.root.after(0, lambda: self.progress_text.config(text="API tests completed"))
                self.root.after(0, lambda: self.scan_status.set("✅ Ready"))
                self.status_text.set("API tests completed")

                # Reset after 3 seconds
                def reset_status():
                    time.sleep(3)
                    self.root.after(0, lambda: self.scan_progress.set(0))
                    self.root.after(0, lambda: self.progress_text.config(text="Ready"))
                    self.root.after(0, lambda: self.scan_status.set("💤 Idle"))

                threading.Thread(target=reset_status, daemon=True).start()

            except Exception as e:
                self.log_message(f"❌ API test error: {e}")
                self.status_text.set(f"API test failed: {e}")
                self.root.after(0, lambda: self.scan_status.set("❌ Error"))
                self.root.after(0, lambda: self.progress_text.config(text="API test failed"))

        threading.Thread(target=run_tests, daemon=True).start()
    
    def start_trading(self):
        """Start automated trading"""
        if not self.data_feed.is_market_open():
            messagebox.showwarning("Market Closed",
                                 "Market is currently closed. Trading will start when market opens.")

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_text.set("Trading started")
        self.scan_status.set("🚀 Starting...")
        self.scan_progress.set(0)
        self.progress_text.config(text="Initializing trading system...")
        self.log_message("🚀 Automated trading started")

        # Start trading thread
        self.trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
        self.trading_thread.start()

    def stop_trading(self):
        """Stop automated trading"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_text.set("Trading stopped")
        self.scan_status.set("🛑 Stopped")
        self.scan_progress.set(0)
        self.next_scan_countdown.set("")
        self.progress_text.config(text="Trading stopped")
        self.log_message("🛑 Automated trading stopped")
    
    def trading_loop(self):
        """Main trading loop (runs in separate thread)"""
        scan_count = 0

        while self.is_running:
            try:
                scan_count += 1

                # Update scan status
                self.root.after(0, lambda: self.scan_status.set("🔍 Scanning..."))
                self.root.after(0, lambda: self.progress_text.config(text=f"Scan #{scan_count} - Analyzing market..."))

                # Progress: Start scanning
                self.root.after(0, lambda: self.scan_progress.set(10))

                # Import here to avoid circular imports
                from screener import scan_market
                from trade_executor import execute_trade

                # Progress: Getting market data
                self.root.after(0, lambda: self.progress_text.config(text="Getting market data..."))
                self.root.after(0, lambda: self.scan_progress.set(30))

                # Scan for signals
                self.log_message(f"🔍 Starting scan #{scan_count}...")
                signals = scan_market()

                # Progress: Analyzing signals
                self.root.after(0, lambda: self.progress_text.config(text="Analyzing signals..."))
                self.root.after(0, lambda: self.scan_progress.set(60))

                if signals:
                    self.log_message(f"📈 Found {len(signals)} signal(s)")
                    self.root.after(0, lambda: self.progress_text.config(text=f"Processing {len(signals)} signals..."))

                    for i, signal in enumerate(signals):
                        if not self.is_running:
                            break

                        # Progress: Processing signals
                        progress = 60 + (30 * (i + 1) / len(signals))
                        self.root.after(0, lambda p=progress: self.scan_progress.set(p))

                        self.log_message(f"   • {signal['symbol']} | {signal['side'].upper()}")

                        # Execute trade
                        result = execute_trade(signal)
                        if result:
                            self.log_message(f"   ✅ Order placed: {result.get('id', 'N/A')}")
                        else:
                            self.log_message(f"   ❌ Failed to place order")
                else:
                    self.log_message("📊 No signals found")

                # Progress: Complete
                self.root.after(0, lambda: self.scan_progress.set(100))
                self.root.after(0, lambda: self.progress_text.config(text="Scan complete"))

                # Update data periodically
                self.root.after(0, self.refresh_data)

                # Update scan status to waiting
                self.root.after(0, lambda: self.scan_status.set("⏰ Waiting"))

                # Countdown timer
                for remaining in range(TRADING_CONFIG['scan_interval'], 0, -1):
                    if not self.is_running:
                        break

                    minutes, seconds = divmod(remaining, 60)
                    countdown_text = f"{minutes:02d}:{seconds:02d}"
                    self.root.after(0, lambda t=countdown_text: self.next_scan_countdown.set(t))
                    self.root.after(0, lambda r=remaining: self.progress_text.config(text=f"Next scan in {r}s"))

                    # Progress bar countdown
                    progress = 100 - (remaining / TRADING_CONFIG['scan_interval'] * 100)
                    self.root.after(0, lambda p=progress: self.scan_progress.set(p))

                    time.sleep(1)

                # Reset for next scan
                self.root.after(0, lambda: self.next_scan_countdown.set(""))
                self.root.after(0, lambda: self.scan_progress.set(0))

            except Exception as e:
                self.log_message(f"❌ Trading loop error: {e}")
                self.root.after(0, lambda: self.scan_status.set("❌ Error"))
                self.root.after(0, lambda: self.progress_text.config(text="Error occurred"))
                time.sleep(10)  # Wait before retrying
    
    def update_time(self):
        """Update current time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)  # Update every second
    
    def run(self):
        """Start the GUI application"""
        self.log_message("🖥️  AI AutoTrader GUI started")
        self.update_time()  # Start time updates
        self.root.mainloop()

def main():
    """Main function to run the GUI"""
    app = AutoTraderGUI()
    app.run()

if __name__ == "__main__":
    main()
