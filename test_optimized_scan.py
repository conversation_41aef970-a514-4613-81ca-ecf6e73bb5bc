"""
Test the optimized scanning system
"""
from screener import scan_market
import time

def test_quick_scan():
    """Test the optimized scanning system"""
    print("🔍 TESTING OPTIMIZED MARKET SCAN")
    print("=" * 50)
    print("⚡ Using high-priority symbols only")
    print("📊 Daily timeframe for better data availability")
    print("🎯 TTM Squeeze pattern detection")
    print()
    
    print("🚀 Starting scan...")
    start_time = time.time()
    
    try:
        signals = scan_market()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ SCAN COMPLETED!")
        print(f"⏱️  Duration: {duration:.1f} seconds")
        print(f"🚨 Signals found: {len(signals)}")
        
        if signals:
            print("\n🎯 DETECTED SIGNALS:")
            for i, signal in enumerate(signals, 1):
                print(f"\n{i}. {signal['symbol']} - TTM Squeeze Breakout")
                print(f"   📍 Price: ${signal['price']:.2f}")
                print(f"   🎯 Confidence: {signal['confidence']:.3f}")
                print(f"   📊 Squeeze Ratio: {signal.get('squeeze_ratio', 'N/A')}")
                print(f"   📈 Momentum: {signal.get('momentum', 'N/A')}")
                print(f"   ✅ Pattern: {signal.get('entry_reason', 'TTM Squeeze')}")
        else:
            print("\n📊 No TTM Squeeze signals detected")
            print("💡 This is normal - the pattern is specific and rare")
            print("🎯 System is working correctly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Scan failed: {e}")
        return False

def main():
    """Main test function"""
    print("🤖 AI AUTOTRADER - OPTIMIZED SCAN TEST")
    print("=" * 60)
    
    success = test_quick_scan()
    
    if success:
        print(f"\n✅ OPTIMIZED SCANNING SYSTEM WORKING!")
        print(f"🚀 Ready for live trading")
        print(f"⚡ Faster scanning with high-priority symbols")
        print(f"📊 Better data reliability with daily timeframes")
    else:
        print(f"\n❌ Issues detected - check logs for details")

if __name__ == "__main__":
    main()
