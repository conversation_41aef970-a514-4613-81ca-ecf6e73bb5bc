"""
Debug exactly why AAPL signal detection is failing
"""
from data_feed import get_stock_data
from screener import calculate_ttm_squeeze_indicators
import pandas as pd
import numpy as np

def debug_signal_conditions():
    """Debug each condition in the signal detection"""
    print("🔍 DEBUGGING SIGNAL DETECTION CONDITIONS")
    print("=" * 60)
    
    symbol = 'AAPL'
    
    # Get real data
    df = get_stock_data(symbol, timeframe='1d', period=30)
    df = calculate_ttm_squeeze_indicators(df)
    
    # Simulate yellow bar
    df_sim = df.copy()
    df_sim.loc[df_sim.index[-1], 'momentum'] = 0.001
    df_sim['hist_color'] = df_sim['momentum'].apply(
        lambda x: 'yellow' if x > 0 else 'red' if x < 0 else 'gray'
    )
    
    # Get recent data for analysis
    recent = df_sim.tail(min(8, len(df_sim)))
    
    print(f"📊 Data available: {len(df_sim)} bars")
    print(f"📊 Recent data: {len(recent)} bars")
    print()
    
    # Check each condition manually
    print("🔍 CONDITION CHECKS:")
    print("-" * 40)
    
    # 1. Red bars pattern
    has_recent_reds = any(recent['hist_color'] == 'red')
    print(f"1. Has recent reds: {has_recent_reds}")
    if 'hist_color' in recent.columns:
        colors = recent['hist_color'].tolist()
        print(f"   Colors: {colors}")
    
    # 2. Current bar yellow
    current_bar_yellow = recent['hist_color'].iloc[-1] == 'yellow'
    print(f"2. Current bar yellow: {current_bar_yellow}")
    
    # 3. Momentum improving
    momentum_vals = recent['momentum'].dropna()
    print(f"3. Momentum data: {len(momentum_vals)} values")
    if len(momentum_vals) >= 2:
        momentum_improving = momentum_vals.iloc[-1] > momentum_vals.iloc[-2]
        print(f"   Momentum improving: {momentum_improving}")
        print(f"   Last 2 values: {momentum_vals.iloc[-2]:.6f} → {momentum_vals.iloc[-1]:.6f}")
    else:
        momentum_improving = True
        print(f"   Momentum improving: {momentum_improving} (assumed)")
    
    # 4. ATR blue
    atr_blue_on_signal = recent['atr_signal'].iloc[-1] == 'blue'
    print(f"4. ATR blue: {atr_blue_on_signal}")
    print(f"   ATR signal: {recent['atr_signal'].iloc[-1]}")
    
    # 5. Squeeze condition
    current_ratio = recent['squeeze_ratio'].iloc[-1]
    squeeze_ratios = recent['squeeze_ratio'].dropna()
    
    if len(squeeze_ratios) >= 2:
        was_recently_squeezed = any(squeeze_ratios < 2.2) or current_ratio < 2.5
    else:
        was_recently_squeezed = current_ratio < 2.5
    
    print(f"5. Was recently squeezed: {was_recently_squeezed}")
    print(f"   Current ratio: {current_ratio:.2f}")
    
    # 6. Near squeeze exit
    near_squeeze_exit = 1.5 <= current_ratio <= 3.0
    print(f"6. Near squeeze exit: {near_squeeze_exit}")
    print(f"   Ratio range: 1.5 ≤ {current_ratio:.2f} ≤ 3.0")
    
    # 7. Volume confirmation
    if 'volume' in df_sim.columns and len(df_sim) >= 10:
        volume_period = min(len(df_sim), 10)
        avg_volume = df_sim['volume'].rolling(volume_period).mean().iloc[-1]
        current_volume = df_sim['volume'].iloc[-1]
        volume_confirmed = current_volume >= (avg_volume * 0.8)
        volume_ratio = current_volume / avg_volume
    else:
        volume_confirmed = True
        volume_ratio = 1.0
    
    print(f"7. Volume confirmed: {volume_confirmed}")
    print(f"   Current: {current_volume:,.0f}")
    print(f"   Average: {avg_volume:,.0f}")
    print(f"   Ratio: {volume_ratio:.2f} (need ≥ 0.8)")
    
    print()
    print("🎯 FINAL VALIDATION:")
    print("-" * 40)
    
    all_conditions = [
        has_recent_reds,
        current_bar_yellow,
        momentum_improving,
        atr_blue_on_signal,
        was_recently_squeezed,
        near_squeeze_exit,
        volume_confirmed
    ]
    
    condition_names = [
        "Recent reds",
        "Current yellow",
        "Momentum improving",
        "ATR blue",
        "Recently squeezed",
        "Near squeeze exit",
        "Volume confirmed"
    ]
    
    for i, (condition, name) in enumerate(zip(all_conditions, condition_names)):
        status = "✅" if condition else "❌"
        print(f"{status} {name}: {condition}")
    
    all_met = all(all_conditions)
    print()
    print(f"🎯 ALL CONDITIONS MET: {all_met}")
    
    if not all_met:
        failed = [name for condition, name in zip(all_conditions, condition_names) if not condition]
        print(f"❌ Failed conditions: {', '.join(failed)}")
    else:
        print("🎉 All conditions passed! Signal should be detected!")

def main():
    debug_signal_conditions()

if __name__ == "__main__":
    main()
