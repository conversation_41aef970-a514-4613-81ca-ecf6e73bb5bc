"""
Debug AAPL data to understand the price and pattern issues
"""
from data_feed import get_stock_data
import pandas as pd

def debug_aapl_data():
    """Debug AAPL data issues"""
    print("🔍 DEBUGGING AAPL DATA")
    print("=" * 50)
    
    symbol = 'AAPL'
    
    # Get raw data
    df = get_stock_data(symbol, timeframe='1h', period=20)
    
    if df.empty:
        print("❌ No data retrieved")
        return
    
    print(f"✅ Retrieved {len(df)} bars")
    print(f"📅 Date range: {df.index[0]} to {df.index[-1]}")
    
    # Show raw OHLCV data
    print(f"\n📊 Raw OHLCV Data (last 5 bars):")
    recent = df.tail(5)
    for i, (timestamp, row) in enumerate(recent.iterrows()):
        print(f"  {i+1}. {timestamp}")
        print(f"     O: ${row['open']:.2f} H: ${row['high']:.2f}")
        print(f"     L: ${row['low']:.2f} C: ${row['close']:.2f}")
        print(f"     V: {row['volume']:,.0f}")
        print()
    
    # Check if prices are reasonable
    current_price = df['close'].iloc[-1]
    print(f"💰 Current close price: ${current_price:.2f}")
    
    if current_price > 1000:
        print("⚠️  WARNING: Price seems too high for AAPL")
        print("   Expected AAPL price: ~$177-180")
        print("   This might be a data scaling issue")
    elif current_price < 100:
        print("⚠️  WARNING: Price seems too low for AAPL")
    else:
        print("✅ Price seems reasonable for AAPL")
    
    # Check volume
    avg_volume = df['volume'].mean()
    print(f"📊 Average volume: {avg_volume:,.0f}")
    
    if avg_volume < 1000:
        print("⚠️  WARNING: Volume seems very low")
    
    # Check price range
    price_range = df['high'].max() - df['low'].min()
    print(f"📈 Price range: ${price_range:.2f}")
    
    # Show data types
    print(f"\n🔍 Data Types:")
    print(df.dtypes)
    
    # Check for any obvious data issues
    print(f"\n🔍 Data Quality Check:")
    print(f"  Null values: {df.isnull().sum().sum()}")
    print(f"  Negative prices: {(df[['open', 'high', 'low', 'close']] < 0).sum().sum()}")
    print(f"  Zero volumes: {(df['volume'] == 0).sum()}")
    
    # Check OHLC relationships
    ohlc_issues = 0
    for i, row in df.iterrows():
        if row['high'] < row['low']:
            print(f"  ❌ High < Low at {i}")
            ohlc_issues += 1
        if row['high'] < row['open'] or row['high'] < row['close']:
            print(f"  ❌ High < Open/Close at {i}")
            ohlc_issues += 1
        if row['low'] > row['open'] or row['low'] > row['close']:
            print(f"  ❌ Low > Open/Close at {i}")
            ohlc_issues += 1
    
    if ohlc_issues == 0:
        print("  ✅ OHLC relationships look correct")
    else:
        print(f"  ❌ Found {ohlc_issues} OHLC relationship issues")

def main():
    debug_aapl_data()

if __name__ == "__main__":
    main()
