"""
Test the expanded scanning universe
"""
from stock_universe import get_universe_stats, get_full_universe, get_sp500_only, get_large_cap_only, get_etfs_only
from screener import scan_market
import time

def show_universe_breakdown():
    """Show detailed breakdown of scanning universe"""
    print("🎯 AI AUTOTRADER - EXPANDED SCANNING UNIVERSE")
    print("=" * 80)
    
    stats = get_universe_stats()
    
    print(f"📊 TOTAL SCANNING UNIVERSE: {stats['total_symbols']} symbols")
    print()
    print("📈 BREAKDOWN:")
    print(f"  🏢 S&P 500 Stocks: {stats['sp500_count']}")
    print(f"  💰 Large Cap (100B+): {stats['large_cap_count']}")  
    print(f"  📊 Major ETFs: {stats['etf_count']}")
    print()
    
    # Show sample symbols from each category
    sp500 = get_sp500_only()
    large_cap = get_large_cap_only()
    etfs = get_etfs_only()
    
    print("🔍 SAMPLE SYMBOLS:")
    print(f"  S&P 500: {', '.join(sp500[:10])}...")
    print(f"  Large Cap: {', '.join(large_cap[:10])}...")
    print(f"  ETFs: {', '.join(etfs[:10])}...")
    print()
    
    print("🎯 TTM SQUEEZE DETECTION:")
    print("  ✅ 4 consecutive RED bars → YELLOW bar")
    print("  ✅ Price above ATR trailing stop (BLUE signal)")
    print("  ✅ Coming out of squeeze condition")
    print("  ✅ Volume confirmation (≥80% average)")
    print()
    
    print("⚡ SCANNING POWER:")
    print(f"  • {stats['total_symbols']} symbols per scan")
    print(f"  • Every 60 seconds when trading active")
    print(f"  • ~{stats['total_symbols'] * 16} symbol checks per trading day")
    print(f"  • Maximum pattern detection coverage")
    print()
    
    return stats

def estimate_scan_time():
    """Estimate how long a full scan will take"""
    print("⏱️  SCAN TIME ESTIMATION:")
    print("=" * 50)
    
    stats = get_universe_stats()
    
    # Rough estimates based on API call times
    symbols_per_minute = 30  # Conservative estimate with API limits
    total_time_minutes = stats['total_symbols'] / symbols_per_minute
    
    print(f"📊 Symbols to scan: {stats['total_symbols']}")
    print(f"⚡ Estimated rate: ~{symbols_per_minute} symbols/minute")
    print(f"⏱️  Estimated scan time: ~{total_time_minutes:.1f} minutes")
    print()
    
    if total_time_minutes > 5:
        print("💡 OPTIMIZATION SUGGESTIONS:")
        print("  • Scan runs every 60 seconds")
        print("  • Focus on most active symbols first")
        print("  • Parallel processing for speed")
        print("  • Cache data between scans")
    
    return total_time_minutes

def test_sample_scan():
    """Test scanning with a small sample"""
    print("\n🧪 TESTING SAMPLE SCAN:")
    print("=" * 50)
    
    print("🔍 Running sample scan to test system...")
    print("⏱️  This may take a few minutes...")
    
    start_time = time.time()
    
    try:
        signals = scan_market()
        
        end_time = time.time()
        scan_duration = end_time - start_time
        
        print(f"\n✅ SCAN COMPLETED!")
        print(f"⏱️  Duration: {scan_duration:.1f} seconds")
        print(f"🚨 Signals found: {len(signals)}")
        
        if signals:
            print("\n🎯 DETECTED SIGNALS:")
            for i, signal in enumerate(signals, 1):
                print(f"  {i}. {signal['symbol']}")
                print(f"     Price: ${signal['price']:.2f}")
                print(f"     Confidence: {signal['confidence']:.3f}")
                print(f"     Pattern: {signal.get('pattern', 'TTM_Squeeze')}")
        else:
            print("\n📊 No signals detected in current scan")
            print("💡 This is normal - TTM Squeeze patterns are specific")
            print("🎯 System is working correctly, waiting for setups")
        
    except Exception as e:
        print(f"\n❌ Scan test failed: {e}")
        print("💡 This may be due to API limits or data availability")

def main():
    """Main test function"""
    # Show universe breakdown
    stats = show_universe_breakdown()
    
    # Estimate scan time
    estimate_scan_time()
    
    # Ask user if they want to test
    print("🤔 Would you like to run a test scan?")
    print("   This will scan the full universe and may take several minutes...")
    print("   (Press Ctrl+C to skip)")
    
    try:
        input("\nPress Enter to start test scan, or Ctrl+C to skip...")
        test_sample_scan()
    except KeyboardInterrupt:
        print("\n⏭️  Test scan skipped")
    
    print(f"\n🚀 SYSTEM READY!")
    print(f"📊 Configured to scan {stats['total_symbols']} symbols")
    print(f"🎯 TTM Squeeze detection active")
    print(f"💰 Paper trading account ready")
    print(f"🖥️  Launch GUI with: python launch_gui.py")

if __name__ == "__main__":
    main()
