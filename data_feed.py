"""
Enhanced Data Feed with real API integration
"""
import pandas as pd
from datetime import datetime
import logging
from typing import Optional, Dict
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient

logger = logging.getLogger(__name__)

class DataFeed:
    """
    Unified data feed that combines multiple data sources
    """

    def __init__(self):
        """Initialize data feed with Alpaca and FMP API clients ONLY"""
        try:
            self.alpaca_client = AlpacaClient()
            self.fmp_client = FMPClient()
            logger.info("DataFeed initialized successfully - Alpaca + FMP APIs only")
        except Exception as e:
            logger.error(f"Error initializing DataFeed: {e}")
            self.alpaca_client = None
            self.fmp_client = None

    def get_stock_data(self, symbol: str, timeframe: str = '5m',
                      period: int = 100, use_fallback: bool = True) -> pd.DataFrame:
        """
        Get stock data from FMP and Alpaca APIs - FMP FIRST for reliability
        """
        try:
            # Primary: FMP API for reliable historical data (daily only)
            if self.fmp_client and timeframe in ['1d', '1Day']:
                df_fmp = self._get_fmp_data(symbol, period)
                if not df_fmp.empty:
                    logger.info(f"Retrieved {len(df_fmp)} bars from FMP for {symbol}")
                    return df_fmp
                logger.warning(f"No data from FMP for {symbol}")

            # Secondary: Alpaca API for intraday data or FMP fallback
            if self.alpaca_client:
                df = self._get_alpaca_data(symbol, timeframe, period)
                if not df.empty and self._validate_alpaca_data(df, symbol):
                    logger.info(f"Retrieved {len(df)} bars from Alpaca for {symbol}")
                    return df
                logger.warning(f"No valid data from Alpaca for {symbol}")

            # Tertiary: Try FMP for any timeframe if Alpaca fails
            if use_fallback and self.fmp_client and timeframe not in ['1d', '1Day']:
                df_fmp = self._get_fmp_data(symbol, period)
                if not df_fmp.empty:
                    logger.info(f"Retrieved {len(df_fmp)} bars from FMP fallback for {symbol}")
                    return df_fmp

            # NO OTHER FALLBACKS - Return empty DataFrame
            logger.error(f"No real market data available for {symbol} from FMP/Alpaca APIs")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_alpaca_data(self, symbol: str, timeframe: str, period: int) -> pd.DataFrame:
        """Get data from Alpaca API - optimized for real market data"""
        try:
            # Request more data than needed to ensure we get enough bars
            # Don't use start date restriction - just get the most recent data available
            requested_limit = max(period * 3, 100)  # Get at least 100 bars or 3x requested

            df = self.alpaca_client.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                limit=requested_limit
            )

            if df.empty:
                logger.debug(f"No historical data available for {symbol}")
                return pd.DataFrame()

            # Return the requested number of periods (most recent)
            if len(df) > period:
                df = df.tail(period)

            logger.debug(f"Retrieved {len(df)} bars for {symbol} (requested {period})")
            return df

        except Exception as e:
            logger.error(f"Error getting Alpaca data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_fmp_data(self, symbol: str, period: int) -> pd.DataFrame:
        """Get historical data from FMP API as fallback"""
        try:
            if not self.fmp_client:
                return pd.DataFrame()

            # FMP provides historical daily data
            # Get more data than requested to ensure we have enough
            requested_period = max(period * 2, 100)

            # Use FMP historical price endpoint
            historical_data = self.fmp_client.get_historical_prices(symbol, limit=requested_period)

            if not historical_data:
                logger.debug(f"No historical data from FMP for {symbol}")
                return pd.DataFrame()

            # Convert to DataFrame
            df_data = []
            for bar in historical_data:
                df_data.append({
                    'open': float(bar.get('open', 0)),
                    'high': float(bar.get('high', 0)),
                    'low': float(bar.get('low', 0)),
                    'close': float(bar.get('close', 0)),
                    'volume': int(bar.get('volume', 0))
                })

            if not df_data:
                return pd.DataFrame()

            # Create DataFrame with date index
            df = pd.DataFrame(df_data)

            # Create date index from the historical data
            dates = [pd.to_datetime(bar.get('date')) for bar in historical_data]
            df.index = pd.DatetimeIndex(dates)

            # Sort by date (oldest first) and return requested period
            df = df.sort_index()
            if len(df) > period:
                df = df.tail(period)

            logger.debug(f"Retrieved {len(df)} bars from FMP for {symbol}")
            return df

        except Exception as e:
            logger.error(f"Error getting FMP data for {symbol}: {e}")
            return pd.DataFrame()

    def _validate_alpaca_data(self, df: pd.DataFrame, symbol: str) -> bool:
        """Validate Alpaca data for obvious corruption issues"""
        try:
            if df.empty:
                return False

            # Check for reasonable price ranges for major stocks
            current_price = df['close'].iloc[-1]

            # AAPL should be around $150-250 range
            if symbol == 'AAPL':
                if current_price < 100 or current_price > 500:
                    logger.warning(f"AAPL price ${current_price:.2f} seems unrealistic")
                    return False

            # General checks for any stock
            if current_price > 10000:  # No stock should be over $10k
                logger.warning(f"{symbol} price ${current_price:.2f} seems too high")
                return False

            # Check OHLC relationships
            for i, row in df.iterrows():
                if row['high'] < row['low']:
                    logger.warning(f"{symbol} has High < Low at {i}")
                    return False
                if row['high'] < max(row['open'], row['close']):
                    logger.warning(f"{symbol} has High < Open/Close at {i}")
                    return False
                if row['low'] > min(row['open'], row['close']):
                    logger.warning(f"{symbol} has Low > Open/Close at {i}")
                    return False

            # Check volume (should be reasonable for major stocks)
            avg_volume = df['volume'].mean()
            if avg_volume < 1000:  # Very low volume suggests bad data
                logger.warning(f"{symbol} has suspiciously low volume: {avg_volume:.0f}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating data for {symbol}: {e}")
            return False



    def get_fundamental_data(self, symbol: str) -> Optional[Dict]:
        """Get fundamental data for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_company_profile(symbol)
        return None

    def get_financial_ratios(self, symbol: str) -> Optional[Dict]:
        """Get financial ratios for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_financial_ratios(symbol)
        return None

    def get_latest_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote for a symbol"""
        if self.alpaca_client:
            return self.alpaca_client.get_latest_quote(symbol)
        return None

    def is_market_open(self) -> bool:
        """Check if market is open"""
        if self.alpaca_client:
            return self.alpaca_client.is_market_open()

        # Fallback: simple time-based check
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False

        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)

        return market_open <= now <= market_close

# Backward compatibility function
def get_stock_data(symbol: str, timeframe: str = '5m', period: int = 100) -> pd.DataFrame:
    """
    Backward compatible function for existing code
    Returns REAL market data only - NO SIMULATED DATA
    """
    data_feed = DataFeed()
    return data_feed.get_stock_data(symbol, timeframe, period)
