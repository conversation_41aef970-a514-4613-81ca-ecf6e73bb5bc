"""
Test AAPL with FMP data to get reliable prices and patterns
"""
from data_feed import get_stock_data
from screener import calculate_ttm_squeeze_indicators, detect_ttm_squeeze_breakout
import pandas as pd

def test_aapl_with_fmp():
    """Test AAPL with FMP data for reliable pattern detection"""
    print("🍎 TESTING AAPL WITH FMP DATA")
    print("=" * 60)
    print("Using FMP API for reliable historical data")
    print()
    
    symbol = 'AAPL'
    
    # Test daily data from FMP (most reliable)
    print(f"📊 Testing {symbol} with daily data from FMP:")
    
    try:
        # Get daily data (FMP's strength)
        df = get_stock_data(symbol, timeframe='1d', period=30)
        
        if df.empty:
            print(f"  ❌ No data available")
            return
            
        print(f"  ✅ Retrieved {len(df)} daily bars")
        print(f"  📅 Date range: {df.index[0].date()} to {df.index[-1].date()}")
        print(f"  💰 Current price: ${df['close'].iloc[-1]:.2f}")
        
        # Show recent price action
        recent_prices = df.tail(5)
        print(f"\n  📈 Recent price action:")
        for i, (date, row) in enumerate(recent_prices.iterrows()):
            change = row['close'] - row['open']
            change_pct = (change / row['open']) * 100
            color = "🟢" if change > 0 else "🔴" if change < 0 else "⚪"
            print(f"    {date.date()}: ${row['close']:.2f} {color} ({change_pct:+.1f}%)")
        
        # Calculate TTM Squeeze indicators
        print(f"\n  🔍 Calculating TTM Squeeze indicators...")
        df = calculate_ttm_squeeze_indicators(df)
        
        # Show recent pattern
        recent = df.tail(8)
        print(f"\n  🎯 TTM Squeeze Pattern Analysis:")
        
        if 'hist_color' in recent.columns:
            colors = recent['hist_color'].dropna().tolist()
            print(f"    Histogram colors: {' → '.join(colors[-8:])}")
            
            # Count pattern elements
            red_count = sum(1 for c in colors if c == 'red')
            yellow_count = sum(1 for c in colors if c == 'yellow')
            gray_count = sum(1 for c in colors if c == 'gray')
            print(f"    Pattern: {red_count} reds, {yellow_count} yellows, {gray_count} grays")
        
        if 'atr_signal' in recent.columns:
            atr_signals = recent['atr_signal'].dropna().tolist()
            print(f"    ATR signals: {' → '.join(atr_signals[-5:])}")
            current_atr = atr_signals[-1] if atr_signals else 'N/A'
            print(f"    Current ATR: {current_atr} ({'✅ BULLISH' if current_atr == 'blue' else '❌ BEARISH'})")
        
        if 'squeeze_ratio' in recent.columns:
            current_ratio = recent['squeeze_ratio'].iloc[-1]
            if not pd.isna(current_ratio):
                squeeze_status = "🔴 SQUEEZE" if current_ratio < 2.0 else "🟡 TRANSITION" if current_ratio < 2.5 else "🟢 EXPANSION"
                print(f"    Squeeze ratio: {current_ratio:.2f} ({squeeze_status})")
        
        if 'momentum' in recent.columns:
            momentum_vals = recent['momentum'].dropna()
            if len(momentum_vals) >= 2:
                momentum_change = momentum_vals.iloc[-1] - momentum_vals.iloc[-2]
                direction = "📈" if momentum_change > 0 else "📉"
                print(f"    Momentum: {momentum_vals.iloc[-1]:.6f} {direction}")
        
        # Test signal detection
        print(f"\n  🚨 SIGNAL DETECTION TEST:")
        signal = detect_ttm_squeeze_breakout(df, symbol)
        
        if signal:
            print(f"    🎯 *** SIGNAL DETECTED! ***")
            print(f"    📍 Entry Price: ${signal['price']:.2f}")
            print(f"    🎯 Confidence: {signal['confidence']:.3f}")
            print(f"    📊 Pattern: {signal.get('pattern', 'TTM_Squeeze')}")
            print(f"    ✅ Reason: {signal.get('entry_reason', 'Pattern match')}")
            print(f"    📅 Signal Date: {signal.get('timestamp', 'Current')}")
        else:
            print(f"    📊 No signal detected")
            
            # Debug analysis
            print(f"\n  🔍 Debug Analysis:")
            if len(df) >= 5:
                recent_debug = df.tail(5)
                
                # Check each condition
                if 'hist_color' in recent_debug.columns:
                    has_red = any(recent_debug['hist_color'] == 'red')
                    current_yellow = recent_debug['hist_color'].iloc[-1] == 'yellow'
                    print(f"      ✓ Has recent reds: {has_red}")
                    print(f"      ✓ Current yellow: {current_yellow}")
                
                if 'atr_signal' in recent_debug.columns:
                    atr_blue = recent_debug['atr_signal'].iloc[-1] == 'blue'
                    print(f"      ✓ ATR blue (bullish): {atr_blue}")
                
                if 'squeeze_ratio' in recent_debug.columns:
                    ratio = recent_debug['squeeze_ratio'].iloc[-1]
                    if not pd.isna(ratio):
                        in_range = 1.5 <= ratio <= 3.0
                        print(f"      ✓ Squeeze ratio in range: {in_range} ({ratio:.2f})")
                
                if 'momentum' in recent_debug.columns:
                    momentum_vals = recent_debug['momentum'].dropna()
                    if len(momentum_vals) >= 2:
                        improving = momentum_vals.iloc[-1] > momentum_vals.iloc[-2]
                        print(f"      ✓ Momentum improving: {improving}")
                
                # Volume check
                recent_volume = recent_debug['volume'].iloc[-1]
                avg_volume = df['volume'].tail(20).mean()
                volume_ok = recent_volume > avg_volume * 0.5
                print(f"      ✓ Volume adequate: {volume_ok} ({recent_volume:,.0f} vs avg {avg_volume:,.0f})")
        
        print()
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print()

def main():
    """Main test function"""
    test_aapl_with_fmp()
    
    print("🎯 ANALYSIS COMPLETE")
    print("=" * 60)
    print("This test uses FMP data which should be more reliable")
    print("than Alpaca paper trading data for pattern detection.")

if __name__ == "__main__":
    main()
