"""
Test TTM Squeeze detection specifically on AAPL
Based on the chart showing clear red-to-yellow transition
"""
from data_feed import get_stock_data
from screener import calculate_ttm_squeeze_indicators, detect_ttm_squeeze_breakout
import pandas as pd

def test_aapl_detection():
    """Test AAPL specifically for TTM Squeeze pattern"""
    print("🍎 TESTING AAPL TTM SQUEEZE DETECTION")
    print("=" * 60)
    print("Based on chart showing red-to-yellow transition pattern")
    print()
    
    symbol = 'AAPL'
    
    # Try different timeframes to get the best data
    for timeframe in ['1h', '1d']:
        print(f"📊 Testing {symbol} on {timeframe} timeframe:")
        
        try:
            # Get data
            df = get_stock_data(symbol, timeframe=timeframe, period=50)
            
            if df.empty:
                print(f"  ❌ No data available")
                continue
                
            print(f"  ✅ Retrieved {len(df)} bars")
            print(f"  📅 Date range: {df.index[0]} to {df.index[-1]}")
            print(f"  💰 Current price: ${df['close'].iloc[-1]:.2f}")
            
            # Calculate indicators
            df = calculate_ttm_squeeze_indicators(df)
            
            # Show recent pattern
            recent = df.tail(min(8, len(df)))
            print(f"\n  🔍 Recent pattern analysis:")
            
            if 'hist_color' in recent.columns:
                colors = recent['hist_color'].dropna().tolist()
                print(f"    Histogram colors: {' → '.join(colors[-5:])}")
                
                # Count reds and yellows
                red_count = sum(1 for c in colors if c == 'red')
                yellow_count = sum(1 for c in colors if c == 'yellow')
                print(f"    Red bars: {red_count}, Yellow bars: {yellow_count}")
            
            if 'atr_signal' in recent.columns:
                atr_signals = recent['atr_signal'].dropna().tolist()
                print(f"    ATR signals: {' → '.join(atr_signals[-5:])}")
                current_atr = atr_signals[-1] if atr_signals else 'N/A'
                print(f"    Current ATR: {current_atr}")
            
            if 'squeeze_ratio' in recent.columns:
                current_ratio = recent['squeeze_ratio'].iloc[-1]
                if not pd.isna(current_ratio):
                    squeeze_status = "SQUEEZE" if current_ratio < 2.0 else "EXPANSION"
                    print(f"    Squeeze ratio: {current_ratio:.2f} ({squeeze_status})")
            
            if 'momentum' in recent.columns:
                momentum_vals = recent['momentum'].dropna()
                if len(momentum_vals) >= 2:
                    momentum_change = momentum_vals.iloc[-1] - momentum_vals.iloc[-2]
                    direction = "↑" if momentum_change > 0 else "↓"
                    print(f"    Momentum: {momentum_vals.iloc[-1]:.6f} {direction}")
            
            # Test signal detection
            print(f"\n  🎯 Signal Detection Test:")
            signal = detect_ttm_squeeze_breakout(df, symbol)
            
            if signal:
                print(f"    🚨 SIGNAL DETECTED!")
                print(f"    📍 Entry Price: ${signal['price']:.2f}")
                print(f"    🎯 Confidence: {signal['confidence']:.3f}")
                print(f"    📊 Pattern: {signal.get('pattern', 'TTM_Squeeze')}")
                print(f"    ✅ Reason: {signal.get('entry_reason', 'Pattern match')}")
            else:
                print(f"    📊 No signal detected")
                
                # Debug why no signal
                if len(df) >= 3:
                    recent_debug = df.tail(3)
                    print(f"    🔍 Debug info:")
                    
                    if 'hist_color' in recent_debug.columns:
                        has_red = any(recent_debug['hist_color'] == 'red')
                        current_yellow = recent_debug['hist_color'].iloc[-1] == 'yellow'
                        print(f"      Has recent reds: {has_red}")
                        print(f"      Current yellow: {current_yellow}")
                    
                    if 'atr_signal' in recent_debug.columns:
                        atr_blue = recent_debug['atr_signal'].iloc[-1] == 'blue'
                        print(f"      ATR blue: {atr_blue}")
                    
                    if 'squeeze_ratio' in recent_debug.columns:
                        ratio = recent_debug['squeeze_ratio'].iloc[-1]
                        if not pd.isna(ratio):
                            in_range = 1.5 <= ratio <= 3.0
                            print(f"      Squeeze ratio in range: {in_range} ({ratio:.2f})")
            
            print()
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            print()

def main():
    """Main test function"""
    test_aapl_detection()
    
    print("🎯 ANALYSIS COMPLETE")
    print("=" * 60)
    print("If AAPL shows the pattern in your chart but no signal detected,")
    print("we may need to further adjust the detection logic.")

if __name__ == "__main__":
    main()
