"""
Test GUI components without launching full application
"""
import tkinter as tk
from tkinter import ttk
import sys

def test_gui_components():
    """Test basic GUI functionality"""
    try:
        # Test basic tkinter
        root = tk.Tk()
        root.title("AI AutoTrader - GUI Test")
        root.geometry("600x400")
        
        # Test widgets
        ttk.Label(root, text="🤖 AI AutoTrader GUI Test", 
                 font=('Arial', 16, 'bold')).pack(pady=20)
        
        ttk.Label(root, text="✅ Tkinter is working correctly!").pack(pady=10)
        ttk.Label(root, text="✅ TTK themes are available!").pack(pady=10)
        
        # Test variables
        test_var = tk.StringVar(value="$100,000.00")
        ttk.Label(root, text="Account Balance:").pack(pady=5)
        ttk.Label(root, textvariable=test_var, font=('Arial', 12, 'bold')).pack(pady=5)
        
        # Test button
        def on_test_click():
            test_var.set("$150,000.00")
            status_label.config(text="✅ Button clicked successfully!")
        
        ttk.Button(root, text="Test Button", command=on_test_click).pack(pady=10)
        
        status_label = ttk.Label(root, text="Ready for testing...")
        status_label.pack(pady=10)
        
        # Instructions
        ttk.Label(root, text="If you can see this window, the GUI system is working!").pack(pady=20)
        ttk.Label(root, text="Close this window to continue.").pack(pady=5)
        
        print("✅ GUI test window created successfully")
        print("🖥️  Check your screen for the test window")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing GUI components...")
    success = test_gui_components()
    
    if success:
        print("✅ GUI test completed successfully!")
        print("🚀 You can now run the full GUI with: python launch_gui.py")
    else:
        print("❌ GUI test failed!")
        print("💡 Make sure tkinter is installed properly")
    
    input("Press Enter to exit...")
